import random
import string
import uuid
from flask import Flask, render_template, request, redirect, url_for, jsonify, session
from flask_socketio import <PERSON><PERSON><PERSON>, join_room, leave_room
from games.kankei import <PERSON><PERSON><PERSON><PERSON><PERSON>, KankeiPlayer, KankeiBot

# Debug configuration
DEBUG = True

def debug_log(message):
    """Log debug messages if DEBUG is enabled."""
    if DEBUG:
        print(f"[DEBUG] {message}")

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-goes-here'  # Fixed secret key
socketio = SocketIO(app, cors_allowed_origins="*")

# In-memory data store for game lobbies
# Structure:
# {
#     'game_code': {
#         'host': 'host_name',
#         'players': ['player1', 'player2', ...],
#         'max_players': 6,
#         'started': False,
#         'game_type': 'Kankei',  # Default game type
#         'is_public': False,     # Whether the room is publicly visible
#         'display_name': None    # Custom display name for the room (None = use game code)
#     }
# }
game_lobbies = {}

# In-memory data store for active games
# Structure:
# {
#     'game_code': {
#         'game_type': 'Kankei',
#         'game_instance': KankeiGame instance,
#         'player_ids': {
#             'player_name': 'player_id',
#             ...
#         }
#     }
# }
active_games = {}

def generate_game_code():
    """Generate a random 6-character alphanumeric game code."""
    characters = string.ascii_uppercase + string.digits
    while True:
        code = ''.join(random.choice(characters) for _ in range(6))
        if code not in game_lobbies:
            return code

def remove_player_from_lobby(player_name, game_code, action='leave'):
    """
    Remove a player from a lobby and handle host transfer if needed.

    Args:
        player_name: The name of the player to remove
        game_code: The game code of the lobby
        action: The action to take ('leave', 'transfer', or 'end')

    Returns:
        bool: True if successful, False otherwise
    """
    if game_code not in game_lobbies or player_name not in game_lobbies[game_code]['players']:
        return False

    lobby = game_lobbies[game_code]

    # Check if player is the host
    is_host = (player_name == lobby['host'])

    if action == 'end' and is_host:
        # End the game for everyone
        socketio.emit('game_ended', {'ended_by': player_name}, room=game_code)
        del game_lobbies[game_code]
        return True

    # Remove player from the lobby
    lobby['players'].remove(player_name)

    # If the host is leaving and there are other players, transfer host
    if is_host and lobby['players'] and action == 'transfer':
        new_host = lobby['players'][0]
        lobby['host'] = new_host
        socketio.emit('new_host', {'host': new_host}, room=game_code)
    elif is_host and not lobby['players']:
        # If the host is the last player, remove the lobby
        del game_lobbies[game_code]
    else:
        # Notify other players that someone left
        socketio.emit('player_left', {
            'player_name': player_name,
            'players': lobby['players']
        }, room=game_code)

    return True

@app.route('/')
def index():
    """Render the homepage."""
    # Get all public rooms that haven't started yet
    public_rooms = []
    for code, lobby in game_lobbies.items():
        if lobby.get('is_public', False) and not lobby['started']:
            room_info = {
                'code': code,
                'display_name': lobby.get('display_name') or code,
                'host': lobby['host'],
                'player_count': len(lobby['players']),
                'max_players': lobby['max_players'],
                'game_type': lobby['game_type']
            }
            public_rooms.append(room_info)

    return render_template('index.html', public_rooms=public_rooms)

@app.route('/get_public_rooms')
def get_public_rooms():
    """Get a list of all public rooms."""
    public_rooms = []
    for code, lobby in game_lobbies.items():
        if lobby.get('is_public', False) and not lobby['started']:
            room_info = {
                'code': code,
                'display_name': lobby.get('display_name') or code,
                'host': lobby['host'],
                'player_count': len(lobby['players']),
                'max_players': lobby['max_players'],
                'game_type': lobby['game_type']
            }
            public_rooms.append(room_info)

    return jsonify({'success': True, 'rooms': public_rooms})

@app.route('/debug_session')
def debug_session():
    """Debug route to check session data."""
    if not DEBUG:
        return jsonify({"error": "Debug mode is disabled"}), 403

    return jsonify({
        'session': {
            'game_code': session.get('game_code'),
            'player_name': session.get('player_name'),
            'is_host': session.get('is_host')
        },
        'game_lobbies': {code: {
            'host': lobby['host'],
            'players': lobby['players'],
            'max_players': lobby['max_players'],
            'started': lobby['started'],
            'game_type': lobby['game_type']
        } for code, lobby in game_lobbies.items()}
    })

@app.route('/host', methods=['POST'])
def host_game():
    """Create a new game lobby and add the host."""
    player_name = request.form.get('player_name')
    if not player_name:
        return redirect(url_for('index'))

    game_code = generate_game_code()
    game_lobbies[game_code] = {
        'host': player_name,
        'players': [player_name],
        'max_players': 6,
        'started': False,
        'game_type': 'Kankei',  # Default game type
        'is_public': False,     # Default to private
        'display_name': None    # Default to None (will show game code)
    }

    session['player_name'] = player_name
    session['game_code'] = game_code
    session['is_host'] = True

    return redirect(url_for('lobby', game_code=game_code))

@app.route('/join', methods=['POST'])
def join_game():
    """Join an existing game lobby."""
    player_name = request.form.get('player_name')
    game_code = request.form.get('game_code')

    if not player_name or not game_code:
        return redirect(url_for('index'))

    if game_code not in game_lobbies:
        return render_template('index.html', error="Game not found")

    lobby = game_lobbies[game_code]

    if lobby['started']:
        return render_template('index.html', error="Game already started")

    if len(lobby['players']) >= lobby['max_players']:
        return render_template('index.html', error="Game lobby is full")

    if player_name in lobby['players']:
        return render_template('index.html', error="Name already taken in this lobby")

    lobby['players'].append(player_name)

    session['player_name'] = player_name
    session['game_code'] = game_code
    session['is_host'] = False

    # Notify all players in the lobby about the new player
    socketio.emit('player_joined', {'player_name': player_name, 'players': lobby['players']}, room=game_code)

    return redirect(url_for('lobby', game_code=game_code))

@app.route('/lobby/<game_code>')
def lobby(game_code):
    """Render the game lobby page."""
    if game_code not in game_lobbies:
        return redirect(url_for('index'))

    lobby = game_lobbies[game_code]
    player_name = session.get('player_name')
    is_host = session.get('is_host', False)

    if player_name not in lobby['players']:
        return redirect(url_for('index'))

    return render_template('lobby.html',
                          game_code=game_code,
                          players=lobby['players'],
                          max_players=lobby['max_players'],
                          is_host=is_host,
                          host=lobby['host'],
                          game_type=lobby['game_type'],
                          is_public=lobby.get('is_public', False),
                          display_name=lobby.get('display_name'))

@app.route('/update_max_players', methods=['POST'])
def update_max_players():
    """Update the maximum number of players for a game lobby."""
    game_code = request.form.get('game_code')
    player_name = request.form.get('player_name')

    debug_log(f"Update max players - Form data: game_code={game_code}, player_name={player_name}")

    if not game_code or game_code not in game_lobbies:
        return jsonify({'success': False, 'error': 'Game not found'}), 404

    lobby = game_lobbies[game_code]

    # Check if the player is the host
    if player_name != lobby['host']:
        return jsonify({'success': False, 'error': 'Only the host can update max players'}), 403

    try:
        max_players = int(request.form.get('max_players', 6))
        if max_players < len(lobby['players']):
            return jsonify({'success': False, 'error': 'Cannot set max players less than current player count'}), 400

        if max_players < 2 or max_players > 10:
            return jsonify({'success': False, 'error': 'Max players must be between 2 and 10'}), 400

        lobby['max_players'] = max_players
        socketio.emit('max_players_updated', {'max_players': max_players}, room=game_code)
        return jsonify({'success': True})
    except ValueError:
        return jsonify({'success': False, 'error': 'Invalid value'}), 400

@app.route('/update_game_type', methods=['POST'])
def update_game_type():
    """Update the game type for a game lobby."""
    game_code = request.form.get('game_code')
    player_name = request.form.get('player_name')
    game_type = request.form.get('game_type')

    debug_log(f"Update game type - Form data: game_code={game_code}, player_name={player_name}, game_type={game_type}")

    if not game_code or game_code not in game_lobbies:
        return jsonify({'success': False, 'error': 'Game not found'}), 404

    lobby = game_lobbies[game_code]

    # Check if the player is the host
    if player_name != lobby['host']:
        return jsonify({'success': False, 'error': 'Only the host can update game type'}), 403

    # Validate game type
    valid_game_types = ['Kankei', 'Hayabusa', 'Sutoppu', 'Canasta', 'Shichinarabe']
    if game_type not in valid_game_types:
        return jsonify({'success': False, 'error': 'Invalid game type'}), 400

    # Update game type
    lobby['game_type'] = game_type
    socketio.emit('game_type_updated', {'game_type': game_type}, room=game_code)
    return jsonify({'success': True})

@app.route('/update_room_settings', methods=['POST'])
def update_room_settings():
    """Update room visibility and display name."""
    game_code = request.form.get('game_code')
    player_name = request.form.get('player_name')
    is_public = request.form.get('is_public') == 'true'
    display_name = request.form.get('display_name', '').strip() or None

    debug_log(f"Update room settings - Form data: game_code={game_code}, player_name={player_name}, is_public={is_public}, display_name={display_name}")

    if not game_code or game_code not in game_lobbies:
        return jsonify({'success': False, 'error': 'Game not found'}), 404

    lobby = game_lobbies[game_code]

    # Check if the player is the host
    if player_name != lobby['host']:
        return jsonify({'success': False, 'error': 'Only the host can update room settings'}), 403

    # Validate display name (if provided)
    if display_name and len(display_name) > 20:
        return jsonify({'success': False, 'error': 'Display name must be 20 characters or less'}), 400

    # Update room settings
    lobby['is_public'] = is_public
    lobby['display_name'] = display_name

    # Notify all players in the room about the settings update
    socketio.emit('room_settings_updated', {
        'is_public': is_public,
        'display_name': display_name or game_code
    }, room=game_code)

    return jsonify({'success': True})

@app.route('/start_game', methods=['POST'])
def start_game():
    """Start the game."""
    game_code = request.form.get('game_code')
    player_name = request.form.get('player_name')

    debug_log(f"Start game - Form data: game_code={game_code}, player_name={player_name}")

    if not game_code or game_code not in game_lobbies:
        return jsonify({'success': False, 'error': 'Game not found'}), 404

    lobby = game_lobbies[game_code]

    # Check if the player is the host
    if player_name != lobby['host']:
        return jsonify({'success': False, 'error': 'Only the host can start the game'}), 403

    # Mark the lobby as started
    lobby['started'] = True

    # Initialize the game based on the game type
    game_type = lobby['game_type']

    if game_type == 'Kankei':
        # Create a new Kankei game instance
        game_instance = KankeiGame(game_code)

        # Create player IDs mapping
        player_ids = {}

        # Add players to the game
        for player_name in lobby['players']:
            player_id = str(uuid.uuid4())
            player = KankeiPlayer(player_id, player_name)
            game_instance.add_player(player)
            player_ids[player_name] = player_id

        # Fill with bots if needed (Kankei requires exactly 6 players)
        while len(game_instance.players) < 6:
            bot_id = f"bot-{str(uuid.uuid4())[:8]}"
            bot = KankeiBot(bot_id)
            game_instance.add_player(bot)

        # Store the game instance
        active_games[game_code] = {
            'game_type': game_type,
            'game_instance': game_instance,
            'player_ids': player_ids
        }

        # Start the game
        game_instance.start_game()

        # Emit game started event with redirect URL
        socketio.emit('game_started', {
            'game_type': game_type,
            'redirect_url': f'/games/kankei/{game_code}'
        }, room=game_code)

        # Schedule bot turns to be processed if the first player is a bot
        schedule_bot_turns(game_code)
    else:
        # For other game types (not implemented yet)
        socketio.emit('game_started', {'game_type': game_type}, room=game_code)

    return jsonify({'success': True, 'game_type': game_type})

@app.route('/games/kankei/<game_code>')
def kankei_game(game_code):
    """Render the Kankei game page."""
    debug_log(f"Kankei game route - game_code={game_code}, session={dict(session)}")

    if game_code not in game_lobbies or game_code not in active_games:
        debug_log(f"Game not found - game_code={game_code}")
        return redirect(url_for('index'))

    lobby = game_lobbies[game_code]
    player_name = session.get('player_name')

    debug_log(f"Player check - player_name={player_name}, lobby_players={lobby['players']}")

    if not player_name or player_name not in lobby['players']:
        debug_log(f"Player not in lobby - redirecting to index")
        return redirect(url_for('index'))

    # Get the player's ID
    player_id = active_games[game_code]['player_ids'].get(player_name)
    if not player_id:
        debug_log(f"Player ID not found - player_name={player_name}")
        return redirect(url_for('index'))

    debug_log(f"Rendering Kankei game - player_id={player_id}, player_name={player_name}")

    return render_template('games/kankei.html',
                          game_code=game_code,
                          player_id=player_id,
                          player_name=player_name)

@socketio.on('connect')
def handle_connect():
    """Handle client connection to WebSocket."""
    game_code = session.get('game_code')
    player_name = session.get('player_name')

    debug_log(f"WebSocket connect - game_code={game_code}, player_name={player_name}")

    if game_code:
        join_room(game_code)
        debug_log(f"Player {player_name} joined room {game_code}")

@app.route('/exit_game', methods=['POST'])
def exit_game():
    """Handle a player leaving the game."""
    player_name = request.form.get('player_name')
    game_code = request.form.get('game_code')
    action = request.form.get('action', 'leave')  # 'leave', 'transfer', or 'end'

    debug_log(f"Exit game - Form data: game_code={game_code}, player_name={player_name}, action={action}")

    if not game_code or game_code not in game_lobbies:
        return jsonify({'success': False, 'error': 'Game not found'}), 404

    lobby = game_lobbies[game_code]

    if player_name not in lobby['players']:
        return jsonify({'success': False, 'error': 'Player not in game'}), 400

    # Use the helper function to remove the player
    remove_player_from_lobby(player_name, game_code, action)

    # Clear session data
    session.pop('player_name', None)
    session.pop('game_code', None)
    session.pop('is_host', None)

    return jsonify({'success': True})

@socketio.on('disconnect')
def handle_disconnect():
    """Handle client disconnection from WebSocket."""
    player_name = session.get('player_name')
    game_code = session.get('game_code')

    if game_code and game_code in game_lobbies and player_name:
        # Use the helper function to remove the player
        if remove_player_from_lobby(player_name, game_code, 'leave'):
            leave_room(game_code)

# Kankei game WebSocket event handlers
@socketio.on('kankei_request_state')
def handle_kankei_request_state(data):
    """Handle request for current Kankei game state."""
    game_code = data.get('game_code')
    player_id = data.get('player_id')

    if not game_code or game_code not in active_games:
        return

    game_data = active_games[game_code]
    if game_data['game_type'] != 'Kankei':
        return

    game_instance = game_data['game_instance']

    # Get the game state for the player
    game_state = game_instance.get_game_state(player_id)

    # Emit the game state to the player
    socketio.emit('kankei_game_state', game_state, room=request.sid)

@socketio.on('kankei_play_card')
def handle_kankei_play_card(data):
    """Handle a player playing a card in Kankei."""
    game_code = data.get('game_code')
    player_id = data.get('player_id')
    card_index = data.get('card_index')

    if not game_code or game_code not in active_games:
        socketio.emit('kankei_error', {'error': 'Game not found'}, room=request.sid)
        return

    game_data = active_games[game_code]
    if game_data['game_type'] != 'Kankei':
        socketio.emit('kankei_error', {'error': 'Not a Kankei game'}, room=request.sid)
        return

    game_instance = game_data['game_instance']

    # Process the card play
    result = game_instance.play_card(player_id, card_index)

    if not result.get('success', False):
        socketio.emit('kankei_error', {'error': result.get('error', 'Unknown error')}, room=request.sid)
        return

    # Emit card played event to all players
    socketio.emit('kankei_card_played', {
        'player_id': player_id,
        'card_played': result['card_played'],
        'next_player_id': result['next_player_id'],
        'trick_size': result['trick_size']
    }, room=game_code)

    # If the trick is complete, emit trick complete event
    if game_instance.game_state == KankeiGame.STATE_TRICK_EVALUATION:
        trick_result = result  # The result already contains trick evaluation data
        socketio.emit('kankei_trick_complete', {
            'trick_winner': trick_result['trick_winner'],
            'next_player_id': trick_result['next_player_id'],
            'trick_number': trick_result['trick_number']
        }, room=game_code)

    # If the round has ended, emit round ended event
    if game_instance.game_state == KankeiGame.STATE_ROUND_END:
        round_result = game_instance.end_round()
        socketio.emit('kankei_round_ended', {
            'scores': round_result['scores'],
            'new_dealer': round_result['new_dealer'],
            'new_trump_suit': round_result['new_trump_suit']
        }, room=game_code)

    # Schedule bot turns to be processed
    schedule_bot_turns(game_code)

# Handle bot turns
def process_bot_turns(game_code):
    """Process turns for bot players in a Kankei game."""
    if game_code not in active_games:
        return

    game_data = active_games[game_code]
    if game_data['game_type'] != 'Kankei':
        return

    game_instance = game_data['game_instance']

    # Check if it's a bot's turn
    if game_instance.game_state != KankeiGame.STATE_PLAYING:
        return

    current_player = game_instance.players[game_instance.current_player_index]

    # If it's the dealer's turn, skip (dealer doesn't play)
    if game_instance.current_player_index == game_instance.dealer_index:
        return

    # If it's not a bot's turn, return
    if not hasattr(current_player, 'is_bot') or not current_player.is_bot:
        return

    # Get the game state
    game_state = game_instance.get_game_state(current_player.id)

    # Let the bot choose a card
    card_index = current_player.choose_card(game_state)

    if card_index is not None:
        # Process the bot's card play
        result = game_instance.play_card(current_player.id, card_index)

        if result.get('success', False):
            # Emit card played event to all players
            socketio.emit('kankei_card_played', {
                'player_id': current_player.id,
                'card_played': result['card_played'],
                'next_player_id': result['next_player_id'],
                'trick_size': result['trick_size']
            }, room=game_code)

            # If the trick is complete, emit trick complete event
            if game_instance.game_state == KankeiGame.STATE_TRICK_EVALUATION:
                trick_result = result  # The result already contains trick evaluation data
                socketio.emit('kankei_trick_complete', {
                    'trick_winner': trick_result['trick_winner'],
                    'next_player_id': trick_result['next_player_id'],
                    'trick_number': trick_result['trick_number']
                }, room=game_code)

            # If the round has ended, emit round ended event
            if game_instance.game_state == KankeiGame.STATE_ROUND_END:
                round_result = game_instance.end_round()
                socketio.emit('kankei_round_ended', {
                    'scores': round_result['scores'],
                    'new_dealer': round_result['new_dealer'],
                    'new_trump_suit': round_result['new_trump_suit']
                }, room=game_code)

            # Process the next turn if it's still a bot's turn
            process_bot_turns(game_code)

# Process bot turns after a human player's turn
def schedule_bot_turns(game_code):
    """Schedule bot turns to be processed after a delay."""
    import threading
    import time

    def delayed_bot_processing():
        time.sleep(0.5)  # Small delay to ensure the previous action is processed
        process_bot_turns(game_code)

    thread = threading.Thread(target=delayed_bot_processing)
    thread.daemon = True
    thread.start()

if __name__ == '__main__':
    socketio.run(app, debug=True)
