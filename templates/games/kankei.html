<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kankei - {{ game_code }}</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/games/kankei.css') }}">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
</head>
<body>
    <div class="kankei-game">
        <!-- Game information bar -->
        <div class="game-info-bar">
            <div class="trump-indicator">
                <span class="label">Trump:</span>
                <div class="trump-suit" id="trump-suit"></div>
            </div>
            <div class="trick-counter">
                <span class="label">Trick:</span>
                <span id="current-trick-number">0</span>/10
            </div>
            <div class="score-toggle">
                <button id="toggle-score-btn" class="btn btn--secondary">Show Scores</button>
            </div>
            <div class="exit-game">
                <button id="exit-game-btn" class="btn btn--outline">Exit Game</button>
            </div>
        </div>

        <!-- Game table -->
        <div class="game-table">
            <!-- Player positions -->
            <div class="player player-0" id="player-0">
                <div class="player-info">
                    <div class="player-name"></div>
                    <div class="player-team"></div>
                    <div class="dealer-marker hidden">D</div>
                </div>
                <div class="player-hand"></div>
            </div>

            <div class="player player-1" id="player-1">
                <div class="player-info">
                    <div class="player-name"></div>
                    <div class="player-team"></div>
                    <div class="dealer-marker hidden">D</div>
                </div>
                <div class="player-hand"></div>
            </div>

            <div class="player player-2" id="player-2">
                <div class="player-info">
                    <div class="player-name"></div>
                    <div class="player-team"></div>
                    <div class="dealer-marker hidden">D</div>
                </div>
                <div class="player-hand"></div>
            </div>

            <div class="player player-3" id="player-3">
                <div class="player-info">
                    <div class="player-name"></div>
                    <div class="player-team"></div>
                    <div class="dealer-marker hidden">D</div>
                </div>
                <div class="player-hand"></div>
            </div>

            <div class="player player-4" id="player-4">
                <div class="player-info">
                    <div class="player-name"></div>
                    <div class="player-team"></div>
                    <div class="dealer-marker hidden">D</div>
                </div>
                <div class="player-hand"></div>
            </div>

            <div class="player player-5" id="player-5">
                <div class="player-info">
                    <div class="player-name"></div>
                    <div class="player-team"></div>
                    <div class="dealer-marker hidden">D</div>
                </div>
                <div class="player-hand"></div>
            </div>

            <!-- Center of table for current trick -->
            <div class="table-center">
                <div class="current-trick" id="current-trick"></div>
            </div>
        </div>

        <!-- Player's hand (current player) -->
        <div class="current-player-hand" id="current-player-hand"></div>

        <!-- Game messages -->
        <div class="game-messages" id="game-messages"></div>
    </div>

    <!-- Score display (initially hidden) -->
    <div class="score-display hidden" id="score-display">
        <div class="score-header">
            <h3>Scores</h3>
            <button id="close-score-btn" class="btn btn--outline">Close</button>
        </div>
        <div class="score-table-container">
            <table class="score-table" id="score-table">
                <thead id="score-table-header">
                    <!-- Header will be dynamically generated -->
                </thead>
                <tbody id="score-table-body">
                    <!-- Score table content will be dynamically generated -->
                </tbody>
            </table>
        </div>
    </div>

    <!-- Exit confirmation modal -->
    <div id="exit-modal" class="modal hidden">
        <div class="modal__content">
            <h3 class="modal__title">Exit Game</h3>
            <p class="modal__text">Are you sure you want to exit the game?</p>
            <div class="modal__actions">
                <button id="cancel-exit-btn" class="btn btn--outline">Cancel</button>
                <button id="confirm-exit-btn" class="btn btn--primary">Exit Game</button>
            </div>
        </div>
    </div>

    <script>
        // Store session data for JavaScript
        const gameCode = "{{ game_code }}";
        const playerId = "{{ player_id }}";
        const playerName = "{{ player_name }}";
    </script>
    <script src="{{ url_for('static', filename='js/games/kankei.js') }}"></script>
</body>
</html>
