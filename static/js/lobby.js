document.addEventListener('DOMContentLoaded', function() {
    // Connect to WebSocket server
    const socket = io();

    // DOM elements
    const playersList = document.getElementById('players-list');
    const playerCount = document.getElementById('player-count');
    const maxPlayersCount = document.getElementById('max-players-count');
    const maxPlayersForm = document.getElementById('max-players-form');
    const maxPlayersInput = document.getElementById('max-players');
    const maxPlayersDisplay = document.getElementById('max-players-display');
    const gameTypeForm = document.getElementById('game-type-form');
    const gameTypeInput = document.getElementById('game-type');
    const gameTypeDisplay = document.getElementById('game-type-display');
    const startGameBtn = document.getElementById('start-game-btn');
    const exitGameBtn = document.getElementById('exit-game-btn');
    const hostNameDisplay = document.getElementById('host-name');

    // Modal elements
    const hostExitModal = document.getElementById('host-exit-modal');
    const transferHostBtn = document.getElementById('transfer-host-btn');
    const endGameBtn = document.getElementById('end-game-btn');
    const cancelExitBtn = document.getElementById('cancel-exit-btn');

    // Update players list
    function updatePlayersList(players, host) {
        playersList.innerHTML = '';
        playerCount.textContent = players.length;

        players.forEach(player => {
            const li = document.createElement('li');
            li.className = 'player-item';

            // Determine what indicators to show
            const isHost = (player === host);
            const isCurrentPlayer = (player === playerName);

            // Create player name span
            const nameSpan = document.createElement('span');
            nameSpan.className = 'player-item__name';
            nameSpan.textContent = player;
            li.appendChild(nameSpan);

            // Add host badge if needed
            if (isHost) {
                const hostBadge = document.createElement('span');
                hostBadge.className = 'player-item__badge player-item__badge--host';
                hostBadge.textContent = 'Host';
                li.appendChild(hostBadge);
            }

            // Add current player badge if needed
            if (isCurrentPlayer) {
                const youBadge = document.createElement('span');
                youBadge.className = 'player-item__badge player-item__badge--you';
                youBadge.textContent = 'You';
                li.appendChild(youBadge);
            }

            playersList.appendChild(li);
        });
    }

    // Socket event handlers
    socket.on('connect', function() {
        console.log('Connected to server');
    });

    socket.on('player_joined', function(data) {
        console.log('Player joined:', data.player_name);
        updatePlayersList(data.players, hostNameDisplay.textContent);
    });

    socket.on('player_left', function(data) {
        console.log('Player left:', data.player_name);
        updatePlayersList(data.players, hostNameDisplay.textContent);
    });

    socket.on('max_players_updated', function(data) {
        console.log('Max players updated:', data.max_players);
        maxPlayersCount.textContent = data.max_players;

        if (maxPlayersDisplay) {
            maxPlayersDisplay.textContent = data.max_players;
        }

        if (maxPlayersInput) {
            maxPlayersInput.value = data.max_players;
        }
    });

    socket.on('game_type_updated', function(data) {
        console.log('Game type updated:', data.game_type);

        if (gameTypeDisplay) {
            // Update the game type display with badge
            gameTypeDisplay.innerHTML = `<span class="game-type-badge game-type-badge--${data.game_type.toLowerCase()}">${data.game_type}</span>`;
        }

        if (gameTypeInput) {
            gameTypeInput.value = data.game_type;
        }
    });

    socket.on('room_settings_updated', function(data) {
        console.log('Room settings updated:', data);

        // Update room display name in the UI if needed
        // This could be used to update the page title or other elements
        const displayName = data.display_name;

        // If we have UI elements that show the room name, update them here
        // For example, you might want to update the page title
        document.title = `Game Lobby - ${displayName}`;

        // Update form fields if they exist
        const roomDisplayNameInput = document.getElementById('room-display-name');
        const roomIsPublicCheckbox = document.getElementById('room-is-public');

        if (roomDisplayNameInput) {
            roomDisplayNameInput.value = data.display_name === gameCode ? '' : data.display_name;
        }

        if (roomIsPublicCheckbox) {
            roomIsPublicCheckbox.checked = data.is_public;
        }
    });

    socket.on('new_host', function(data) {
        console.log('New host:', data.host);
        hostNameDisplay.textContent = data.host;

        // If the current user is the new host, reload the page to show host controls
        if (data.host === playerName) {
            window.location.reload();
        } else {
            // Extract player names from the list items, removing any indicators
            const players = Array.from(playersList.querySelectorAll('.player-item')).map(item => {
                return item.textContent.replace(' (Host)', '').replace(' (You)', '').trim();
            });
            updatePlayersList(players, data.host);
        }
    });

    socket.on('game_started', function(data) {
        console.log('Game started with game type:', data.game_type);

        // If a redirect URL is provided, navigate to it
        if (data.redirect_url) {
            window.location.href = data.redirect_url;
        } else {
            alert(`The game has started! Game type: ${data.game_type}`);
        }
    });

    socket.on('game_ended', function(data) {
        console.log('Game ended by:', data.ended_by);
        alert(`The game has been ended by ${data.ended_by}`);
        window.location.href = '/';
    });

    // Event listeners for host controls
    if (isHost && maxPlayersForm) {
        maxPlayersForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const maxPlayers = parseInt(maxPlayersInput.value);
            if (isNaN(maxPlayers) || maxPlayers < 2 || maxPlayers > 10) {
                alert('Max players must be between 2 and 10');
                return;
            }

            // Send request to update max players
            fetch('/update_max_players', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `max_players=${maxPlayers}&game_code=${gameCode}&player_name=${encodeURIComponent(playerName)}`,
                credentials: 'same-origin'  // Include cookies in the request
            })
            .then(response => response.json())
            .then(data => {
                if (!data.success) {
                    alert(data.error || 'Failed to update max players');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while updating max players');
            });
        });
    }

    if (isHost && gameTypeForm) {
        gameTypeForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const gameType = gameTypeInput.value;

            // Send request to update game type
            fetch('/update_game_type', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `game_type=${gameType}&game_code=${gameCode}&player_name=${encodeURIComponent(playerName)}`,
                credentials: 'same-origin'  // Include cookies in the request
            })
            .then(response => response.json())
            .then(data => {
                if (!data.success) {
                    alert(data.error || 'Failed to update game type');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while updating game type');
            });
        });
    }

    // Room settings form handler
    const roomSettingsForm = document.getElementById('room-settings-form');
    if (isHost && roomSettingsForm) {
        roomSettingsForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const displayName = document.getElementById('room-display-name').value.trim();
            const isPublic = document.getElementById('room-is-public').checked;

            // Send request to update room settings
            fetch('/update_room_settings', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `game_code=${gameCode}&player_name=${encodeURIComponent(playerName)}&is_public=${isPublic}&display_name=${encodeURIComponent(displayName)}`,
                credentials: 'same-origin'  // Include cookies in the request
            })
            .then(response => response.json())
            .then(data => {
                if (!data.success) {
                    alert(data.error || 'Failed to update room settings');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while updating room settings');
            });
        });
    }

    if (isHost && startGameBtn) {
        startGameBtn.addEventListener('click', function() {
            // Get current player count
            const currentPlayerCount = parseInt(playerCount.textContent);

            if (currentPlayerCount < 2) {
                alert('You need at least 2 players to start the game');
                return;
            }

            // Send request to start the game
            fetch('/start_game', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `game_code=${gameCode}&player_name=${encodeURIComponent(playerName)}`,
                credentials: 'same-origin'  // Include cookies in the request
            })
            .then(response => response.json())
            .then(data => {
                if (!data.success) {
                    alert(data.error || 'Failed to start the game');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while starting the game');
            });
        });
    }

    // Exit game button click handler
    if (exitGameBtn) {
        exitGameBtn.addEventListener('click', function() {
            if (isHost) {
                // Show the host exit confirmation modal
                hostExitModal.style.display = 'block';
            } else {
                // Regular player exit - just leave
                exitGame('leave');
            }
        });
    }

    // Host exit modal button handlers
    if (transferHostBtn) {
        transferHostBtn.addEventListener('click', function() {
            hostExitModal.style.display = 'none';
            exitGame('transfer');
        });
    }

    if (endGameBtn) {
        endGameBtn.addEventListener('click', function() {
            hostExitModal.style.display = 'none';
            exitGame('end');
        });
    }

    if (cancelExitBtn) {
        cancelExitBtn.addEventListener('click', function() {
            hostExitModal.style.display = 'none';
        });
    }

    // Close modal when clicking outside of it
    window.addEventListener('click', function(event) {
        if (event.target === hostExitModal) {
            hostExitModal.style.display = 'none';
        }
    });

    // Function to handle exiting the game
    function exitGame(action) {
        fetch('/exit_game', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `game_code=${gameCode}&player_name=${encodeURIComponent(playerName)}&action=${action}`,
            credentials: 'same-origin'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Redirect to homepage
                window.location.href = '/';
            } else {
                alert(data.error || 'Failed to exit the game');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while exiting the game');
        });
    }
});
