/**
 * Kankei card game client-side logic
 */
document.addEventListener('DOMContentLoaded', function() {
    // Connect to WebSocket server
    const socket = io();

    // Game state
    let gameState = {
        players: [],
        currentPlayerId: null,
        dealerId: null,
        kanKeiTeam: [],
        muKanKeiTeam: [],
        trumpSuit: null,
        trickNumber: 0,
        currentTrick: [],
        playableCards: []
    };

    // Score history for the new table format
    let scoreHistory = {
        rounds: [], // Array of round data
        trumpSuits: ['clubs', 'diamonds', 'hearts', 'spades'] // Trump rotation
    };

    // DOM elements
    const trumpSuitElement = document.getElementById('trump-suit');
    const currentTrickNumberElement = document.getElementById('current-trick-number');
    const currentTrickElement = document.getElementById('current-trick');
    const currentPlayerHandElement = document.getElementById('current-player-hand');
    const gameMessagesElement = document.getElementById('game-messages');
    const scoreDisplayElement = document.getElementById('score-display');
    const scoreTableHeaderElement = document.getElementById('score-table-header');
    const scoreTableBodyElement = document.getElementById('score-table-body');
    const toggleScoreButton = document.getElementById('toggle-score-btn');
    const closeScoreButton = document.getElementById('close-score-btn');
    const exitGameButton = document.getElementById('exit-game-btn');
    const exitModal = document.getElementById('exit-modal');
    const cancelExitButton = document.getElementById('cancel-exit-btn');
    const confirmExitButton = document.getElementById('confirm-exit-btn');

    // Initialize the game
    function initializeGame() {
        // Set up event listeners
        setupEventListeners();

        // Request initial game state
        socket.emit('kankei_request_state', {
            game_code: gameCode,
            player_id: playerId
        });
    }

    // Set up event listeners
    function setupEventListeners() {
        // Socket events
        socket.on('kankei_game_state', handleGameState);
        socket.on('kankei_card_played', handleCardPlayed);
        socket.on('kankei_trick_complete', handleTrickComplete);
        socket.on('kankei_round_ended', handleRoundEnded);
        socket.on('kankei_error', handleError);

        // UI events
        toggleScoreButton.addEventListener('click', toggleScoreDisplay);
        closeScoreButton.addEventListener('click', toggleScoreDisplay);
        exitGameButton.addEventListener('click', showExitModal);
        cancelExitButton.addEventListener('click', hideExitModal);
        confirmExitButton.addEventListener('click', exitGame);
    }

    // Handle game state update
    function handleGameState(state) {
        gameState = state;

        // Update UI based on game state
        updateGameUI();
    }

    // Update the game UI based on current state
    function updateGameUI() {
        // Update trump suit
        updateTrumpSuit();

        // Update trick number
        currentTrickNumberElement.textContent = gameState.trick_number;

        // Update players
        updatePlayers();

        // Update current trick
        updateCurrentTrick();

        // Update current player's hand
        updateCurrentPlayerHand();

        // Update current player indicator
        updateCurrentPlayerIndicator();
    }

    // Update the trump suit indicator
    function updateTrumpSuit() {
        if (gameState.trump_suit) {
            trumpSuitElement.className = 'trump-suit ' + gameState.trump_suit;
        }
    }

    // Update player information
    function updatePlayers() {
        gameState.players.forEach(player => {
            const playerElement = document.getElementById(`player-${player.seat_position}`);
            if (playerElement) {
                // Update player name
                const nameElement = playerElement.querySelector('.player-name');
                nameElement.textContent = player.name;

                // Update player team
                const teamElement = playerElement.querySelector('.player-team');
                teamElement.textContent = player.team ? (player.team === 'kan-kei' ? 'Kan-kei' : 'Mu-kan-kei') : '';
                teamElement.className = 'player-team';
                if (player.team) {
                    teamElement.classList.add(`team-${player.team}`);
                }

                // Update dealer marker
                const dealerMarker = playerElement.querySelector('.dealer-marker');
                if (player.is_dealer) {
                    dealerMarker.classList.remove('hidden');
                } else {
                    dealerMarker.classList.add('hidden');
                }

                // Update hand (face down for other players)
                if (player.id !== playerId) {
                    const handElement = playerElement.querySelector('.player-hand');
                    handElement.innerHTML = '';

                    // Show face-down cards for other players (only if they have cards)
                    const cardCount = player.hand ? player.hand.length : 0;
                    for (let i = 0; i < cardCount; i++) {
                        const cardElement = document.createElement('div');
                        cardElement.className = 'card face-down';
                        handElement.appendChild(cardElement);
                    }
                }
            }
        });
    }

    // Update the current trick display
    function updateCurrentTrick() {
        currentTrickElement.innerHTML = '';

        gameState.current_trick.forEach((entry, index) => {
            const cardElement = document.createElement('div');
            cardElement.className = `card trick-card-${index}`;
            cardElement.style.backgroundImage = `url('${entry.card.image_path}')`;
            currentTrickElement.appendChild(cardElement);
        });
    }

    // Update the current player's hand
    function updateCurrentPlayerHand() {
        currentPlayerHandElement.innerHTML = '';

        const currentPlayer = gameState.players.find(p => p.id === playerId);
        if (currentPlayer && currentPlayer.hand) {
            currentPlayer.hand.forEach((card, index) => {
                const cardElement = document.createElement('div');
                cardElement.className = 'card';

                // Set card background image
                if (card.image_path) {
                    cardElement.style.backgroundImage = `url('${card.image_path}')`;
                } else {
                    // Fallback for cards without image_path
                    cardElement.classList.add('face-down');
                }

                // Mark playable cards (lifted upward)
                if (gameState.playable_cards && gameState.playable_cards.includes(index)) {
                    cardElement.classList.add('playable');
                }

                // Add click event for playing cards
                cardElement.addEventListener('click', () => {
                    if (gameState.current_player_id === playerId &&
                        gameState.playable_cards &&
                        gameState.playable_cards.includes(index)) {
                        playCard(index);
                    }
                });

                currentPlayerHandElement.appendChild(cardElement);
            });
        }
    }

    // Update current player indicator
    function updateCurrentPlayerIndicator() {
        // Remove current-turn class from all players
        document.querySelectorAll('.player').forEach(element => {
            element.classList.remove('current-turn');
        });

        // Add current-turn class to the current player
        if (gameState.current_player_id) {
            const currentPlayer = gameState.players.find(p => p.id === gameState.current_player_id);
            if (currentPlayer) {
                const playerElement = document.getElementById(`player-${currentPlayer.seat_position}`);
                if (playerElement) {
                    playerElement.classList.add('current-turn');
                }

                // Show message if it's the current user's turn
                if (gameState.current_player_id === playerId) {
                    showGameMessage("Your turn! Play a card.");
                }
            }
        }
    }

    // Play a card
    function playCard(cardIndex) {
        socket.emit('kankei_play_card', {
            game_code: gameCode,
            player_id: playerId,
            card_index: cardIndex
        });
    }

    // Handle card played event
    function handleCardPlayed(data) {
        // Update the game state
        gameState.current_trick.push({
            card: data.card_played,
            player_id: data.player_id
        });

        gameState.current_player_id = data.next_player_id;

        // Update UI
        updateCurrentTrick();
        updateCurrentPlayerIndicator();

        // If it was the current player who played, update their hand
        if (data.player_id === playerId) {
            // Request updated game state to get new hand
            socket.emit('kankei_request_state', {
                game_code: gameCode,
                player_id: playerId
            });
        }
    }

    // Handle trick complete event
    function handleTrickComplete(data) {
        // Show message about who won the trick
        const winner = gameState.players.find(p => p.id === data.trick_winner.id);
        showGameMessage(`${winner.name} won the trick!`);

        // Clear the current trick after a delay
        setTimeout(() => {
            gameState.current_trick = [];
            gameState.current_player_id = data.next_player_id;
            gameState.trick_number = data.trick_number;

            // Update UI
            updateCurrentTrick();
            currentTrickNumberElement.textContent = gameState.trick_number;
            updateCurrentPlayerIndicator();
        }, 2000);
    }

    // Handle round ended event
    function handleRoundEnded(data) {
        // Calculate round points for each player
        const roundScores = {};
        for (const playerId in data.scores) {
            const player = gameState.players.find(p => p.id === playerId);
            if (player) {
                const previousScore = player.score || 0;
                const newScore = data.scores[playerId];
                roundScores[playerId] = newScore - previousScore;
                player.score = newScore;
            }
        }

        // Add round to score history
        scoreHistory.rounds.push({
            trumpSuit: gameState.trump_suit,
            scores: roundScores
        });

        // Show message about round end
        showGameMessage("Round ended! Scores updated.");

        // Update dealer
        gameState.dealer_id = data.new_dealer.id;

        // Update trump suit
        gameState.trump_suit = data.new_trump_suit;

        // Show scores
        updateScoreDisplay();
        toggleScoreDisplay(true);

        // After a delay, request new game state for next round
        setTimeout(() => {
            socket.emit('kankei_request_state', {
                game_code: gameCode,
                player_id: playerId
            });
        }, 5000);
    }

    // Handle error event
    function handleError(data) {
        showGameMessage(`Error: ${data.error}`);
    }

    // Show game message
    function showGameMessage(message) {
        gameMessagesElement.textContent = message;
        gameMessagesElement.classList.add('visible');

        // Hide message after a delay
        setTimeout(() => {
            gameMessagesElement.classList.remove('visible');
        }, 3000);
    }

    // Update score display with new table format
    function updateScoreDisplay() {
        // Clear existing content
        scoreTableHeaderElement.innerHTML = '';
        scoreTableBodyElement.innerHTML = '';

        if (!gameState.players || gameState.players.length === 0) {
            return;
        }

        // Create header row
        const headerRow = document.createElement('tr');

        // Trump suit column header
        const trumpHeader = document.createElement('th');
        trumpHeader.textContent = 'Trump';
        headerRow.appendChild(trumpHeader);

        // Player name headers
        gameState.players.forEach(player => {
            const playerHeader = document.createElement('th');
            playerHeader.textContent = player.name;
            headerRow.appendChild(playerHeader);
        });

        scoreTableHeaderElement.appendChild(headerRow);

        // Create rows for each round
        const maxRounds = Math.max(1, scoreHistory.rounds.length || 1);

        for (let roundIndex = 0; roundIndex < maxRounds; roundIndex++) {
            const row = document.createElement('tr');

            // Trump suit cell
            const trumpCell = document.createElement('td');
            trumpCell.className = 'trump-suit-cell';

            const trumpSuitForRound = scoreHistory.trumpSuits[roundIndex % scoreHistory.trumpSuits.length];
            const trumpIcon = document.createElement('div');
            trumpIcon.className = `trump-suit-icon ${trumpSuitForRound}`;
            trumpCell.appendChild(trumpIcon);

            row.appendChild(trumpCell);

            // Player score cells
            gameState.players.forEach(player => {
                const scoreCell = document.createElement('td');
                scoreCell.className = 'player-score-cell';

                // Get round data if available
                const roundData = scoreHistory.rounds[roundIndex];
                const roundPoints = roundData ? (roundData.scores[player.id] || 0) : 0;
                const cumulativePoints = player.score || 0;

                // Add round points (small number in top-left)
                if (roundPoints > 0) {
                    const roundPointsSpan = document.createElement('span');
                    roundPointsSpan.className = 'round-points';
                    roundPointsSpan.textContent = `+${roundPoints}`;
                    scoreCell.appendChild(roundPointsSpan);
                }

                // Add cumulative points (main number)
                const cumulativePointsSpan = document.createElement('span');
                cumulativePointsSpan.className = 'cumulative-points';
                cumulativePointsSpan.textContent = cumulativePoints;
                scoreCell.appendChild(cumulativePointsSpan);

                row.appendChild(scoreCell);
            });

            scoreTableBodyElement.appendChild(row);
        }
    }

    // Toggle score display
    function toggleScoreDisplay(show) {
        if (show === true || scoreDisplayElement.classList.contains('hidden')) {
            updateScoreDisplay();
            scoreDisplayElement.classList.remove('hidden');
        } else {
            scoreDisplayElement.classList.add('hidden');
        }
    }

    // Show exit modal
    function showExitModal() {
        exitModal.classList.remove('hidden');
    }

    // Hide exit modal
    function hideExitModal() {
        exitModal.classList.add('hidden');
    }

    // Exit game
    function exitGame() {
        window.location.href = '/';
    }

    // Initialize the game
    initializeGame();
});
