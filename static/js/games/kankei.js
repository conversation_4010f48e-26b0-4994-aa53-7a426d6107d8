/**
 * Kankei card game client-side logic
 */
document.addEventListener('DOMContentLoaded', function() {
    // Connect to WebSocket server
    const socket = io();
    
    // Game state
    let gameState = {
        players: [],
        currentPlayerId: null,
        dealerId: null,
        kanKeiTeam: [],
        muKanKeiTeam: [],
        trumpSuit: null,
        trickNumber: 0,
        currentTrick: [],
        playableCards: []
    };
    
    // DOM elements
    const trumpSuitElement = document.getElementById('trump-suit');
    const currentTrickNumberElement = document.getElementById('current-trick-number');
    const currentTrickElement = document.getElementById('current-trick');
    const currentPlayerHandElement = document.getElementById('current-player-hand');
    const gameMessagesElement = document.getElementById('game-messages');
    const scoreDisplayElement = document.getElementById('score-display');
    const scoreTableBodyElement = document.getElementById('score-table-body');
    const toggleScoreButton = document.getElementById('toggle-score-btn');
    const closeScoreButton = document.getElementById('close-score-btn');
    const exitGameButton = document.getElementById('exit-game-btn');
    const exitModal = document.getElementById('exit-modal');
    const cancelExitButton = document.getElementById('cancel-exit-btn');
    const confirmExitButton = document.getElementById('confirm-exit-btn');
    
    // Initialize the game
    function initializeGame() {
        // Set up event listeners
        setupEventListeners();
        
        // Request initial game state
        socket.emit('kankei_request_state', {
            game_code: gameCode,
            player_id: playerId
        });
    }
    
    // Set up event listeners
    function setupEventListeners() {
        // Socket events
        socket.on('kankei_game_state', handleGameState);
        socket.on('kankei_card_played', handleCardPlayed);
        socket.on('kankei_trick_complete', handleTrickComplete);
        socket.on('kankei_round_ended', handleRoundEnded);
        socket.on('kankei_error', handleError);
        
        // UI events
        toggleScoreButton.addEventListener('click', toggleScoreDisplay);
        closeScoreButton.addEventListener('click', toggleScoreDisplay);
        exitGameButton.addEventListener('click', showExitModal);
        cancelExitButton.addEventListener('click', hideExitModal);
        confirmExitButton.addEventListener('click', exitGame);
    }
    
    // Handle game state update
    function handleGameState(state) {
        gameState = state;
        
        // Update UI based on game state
        updateGameUI();
    }
    
    // Update the game UI based on current state
    function updateGameUI() {
        // Update trump suit
        updateTrumpSuit();
        
        // Update trick number
        currentTrickNumberElement.textContent = gameState.trick_number;
        
        // Update players
        updatePlayers();
        
        // Update current trick
        updateCurrentTrick();
        
        // Update current player's hand
        updateCurrentPlayerHand();
        
        // Update current player indicator
        updateCurrentPlayerIndicator();
    }
    
    // Update the trump suit indicator
    function updateTrumpSuit() {
        if (gameState.trump_suit) {
            trumpSuitElement.className = 'trump-suit ' + gameState.trump_suit;
        }
    }
    
    // Update player information
    function updatePlayers() {
        gameState.players.forEach(player => {
            const playerElement = document.getElementById(`player-${player.seat_position}`);
            if (playerElement) {
                // Update player name
                const nameElement = playerElement.querySelector('.player-name');
                nameElement.textContent = player.name;
                
                // Update player team
                const teamElement = playerElement.querySelector('.player-team');
                teamElement.textContent = player.team ? (player.team === 'kan-kei' ? 'Kan-kei' : 'Mu-kan-kei') : '';
                teamElement.className = 'player-team';
                if (player.team) {
                    teamElement.classList.add(`team-${player.team}`);
                }
                
                // Update dealer marker
                const dealerMarker = playerElement.querySelector('.dealer-marker');
                if (player.is_dealer) {
                    dealerMarker.classList.remove('hidden');
                } else {
                    dealerMarker.classList.add('hidden');
                }
                
                // Update hand (face down for other players)
                if (player.id !== playerId) {
                    const handElement = playerElement.querySelector('.player-hand');
                    handElement.innerHTML = '';
                    
                    if (player.hand) {
                        // For debugging, show actual cards
                        player.hand.forEach(card => {
                            const cardElement = document.createElement('div');
                            cardElement.className = 'card face-down';
                            handElement.appendChild(cardElement);
                        });
                    }
                }
            }
        });
    }
    
    // Update the current trick display
    function updateCurrentTrick() {
        currentTrickElement.innerHTML = '';
        
        gameState.current_trick.forEach((entry, index) => {
            const cardElement = document.createElement('div');
            cardElement.className = `card trick-card-${index}`;
            cardElement.style.backgroundImage = `url('${entry.card.image_path}')`;
            currentTrickElement.appendChild(cardElement);
        });
    }
    
    // Update the current player's hand
    function updateCurrentPlayerHand() {
        currentPlayerHandElement.innerHTML = '';
        
        const currentPlayer = gameState.players.find(p => p.id === playerId);
        if (currentPlayer && currentPlayer.hand) {
            currentPlayer.hand.forEach((card, index) => {
                const cardElement = document.createElement('div');
                cardElement.className = 'card';
                cardElement.style.backgroundImage = `url('${card.image_path}')`;
                
                // Mark playable cards
                if (gameState.playable_cards && gameState.playable_cards.includes(index)) {
                    cardElement.classList.add('playable');
                }
                
                // Add click event for playing cards
                cardElement.addEventListener('click', () => {
                    if (gameState.current_player_id === playerId && 
                        gameState.playable_cards && 
                        gameState.playable_cards.includes(index)) {
                        playCard(index);
                    }
                });
                
                currentPlayerHandElement.appendChild(cardElement);
            });
        }
    }
    
    // Update current player indicator
    function updateCurrentPlayerIndicator() {
        // Remove current-turn class from all players
        document.querySelectorAll('.player').forEach(element => {
            element.classList.remove('current-turn');
        });
        
        // Add current-turn class to the current player
        if (gameState.current_player_id) {
            const currentPlayer = gameState.players.find(p => p.id === gameState.current_player_id);
            if (currentPlayer) {
                const playerElement = document.getElementById(`player-${currentPlayer.seat_position}`);
                if (playerElement) {
                    playerElement.classList.add('current-turn');
                }
                
                // Show message if it's the current user's turn
                if (gameState.current_player_id === playerId) {
                    showGameMessage("Your turn! Play a card.");
                }
            }
        }
    }
    
    // Play a card
    function playCard(cardIndex) {
        socket.emit('kankei_play_card', {
            game_code: gameCode,
            player_id: playerId,
            card_index: cardIndex
        });
    }
    
    // Handle card played event
    function handleCardPlayed(data) {
        // Update the game state
        gameState.current_trick.push({
            card: data.card_played,
            player_id: data.player_id
        });
        
        gameState.current_player_id = data.next_player_id;
        
        // Update UI
        updateCurrentTrick();
        updateCurrentPlayerIndicator();
        
        // If it was the current player who played, update their hand
        if (data.player_id === playerId) {
            // Request updated game state to get new hand
            socket.emit('kankei_request_state', {
                game_code: gameCode,
                player_id: playerId
            });
        }
    }
    
    // Handle trick complete event
    function handleTrickComplete(data) {
        // Show message about who won the trick
        const winner = gameState.players.find(p => p.id === data.trick_winner.id);
        showGameMessage(`${winner.name} won the trick!`);
        
        // Clear the current trick after a delay
        setTimeout(() => {
            gameState.current_trick = [];
            gameState.current_player_id = data.next_player_id;
            gameState.trick_number = data.trick_number;
            
            // Update UI
            updateCurrentTrick();
            currentTrickNumberElement.textContent = gameState.trick_number;
            updateCurrentPlayerIndicator();
        }, 2000);
    }
    
    // Handle round ended event
    function handleRoundEnded(data) {
        // Update scores
        for (const playerId in data.scores) {
            const player = gameState.players.find(p => p.id === playerId);
            if (player) {
                player.score = data.scores[playerId];
            }
        }
        
        // Show message about round end
        showGameMessage("Round ended! Scores updated.");
        
        // Update dealer
        gameState.dealer_id = data.new_dealer.id;
        
        // Update trump suit
        gameState.trump_suit = data.new_trump_suit;
        
        // Show scores
        updateScoreDisplay();
        toggleScoreDisplay(true);
        
        // After a delay, request new game state for next round
        setTimeout(() => {
            socket.emit('kankei_request_state', {
                game_code: gameCode,
                player_id: playerId
            });
        }, 5000);
    }
    
    // Handle error event
    function handleError(data) {
        showGameMessage(`Error: ${data.error}`);
    }
    
    // Show game message
    function showGameMessage(message) {
        gameMessagesElement.textContent = message;
        gameMessagesElement.classList.add('visible');
        
        // Hide message after a delay
        setTimeout(() => {
            gameMessagesElement.classList.remove('visible');
        }, 3000);
    }
    
    // Update score display
    function updateScoreDisplay() {
        scoreTableBodyElement.innerHTML = '';
        
        gameState.players.forEach(player => {
            const row = document.createElement('tr');
            
            const nameCell = document.createElement('td');
            nameCell.textContent = player.name;
            row.appendChild(nameCell);
            
            const teamCell = document.createElement('td');
            teamCell.textContent = player.team ? (player.team === 'kan-kei' ? 'Kan-kei' : 'Mu-kan-kei') : '';
            teamCell.className = player.team ? `team-${player.team}` : '';
            row.appendChild(teamCell);
            
            const scoreCell = document.createElement('td');
            scoreCell.textContent = player.score;
            row.appendChild(scoreCell);
            
            scoreTableBodyElement.appendChild(row);
        });
    }
    
    // Toggle score display
    function toggleScoreDisplay(show) {
        if (show === true || scoreDisplayElement.classList.contains('hidden')) {
            updateScoreDisplay();
            scoreDisplayElement.classList.remove('hidden');
        } else {
            scoreDisplayElement.classList.add('hidden');
        }
    }
    
    // Show exit modal
    function showExitModal() {
        exitModal.classList.remove('hidden');
    }
    
    // Hide exit modal
    function hideExitModal() {
        exitModal.classList.add('hidden');
    }
    
    // Exit game
    function exitGame() {
        window.location.href = '/';
    }
    
    // Initialize the game
    initializeGame();
});
