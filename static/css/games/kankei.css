/* Kankei Game Styles */

/* Main game container */
.kankei-game {
    width: 100%;
    height: 100vh;
    position: relative;
    overflow: hidden;
    background-color: #1C7C54; /* Green felt background */
}

/* Game information bar */
.game-info-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 20px;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
}

/* Trump indicator */
.trump-indicator {
    display: flex;
    align-items: center;
}

.trump-suit {
    width: 30px;
    height: 30px;
    margin-left: 10px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
}

.trump-suit.clubs {
    background-image: url('/static/images/Cards/clubs_01.png');
    background-size: 30px;
}

.trump-suit.diamonds {
    background-image: url('/static/images/Cards/diamonds_01.png');
    background-size: 30px;
}

.trump-suit.hearts {
    background-image: url('/static/images/Cards/hearts_01.png');
    background-size: 30px;
}

.trump-suit.spades {
    background-image: url('/static/images/Cards/spades_01.png');
    background-size: 30px;
}

/* Game table */
.game-table {
    position: relative;
    width: 100%;
    height: calc(100vh - 200px);
    margin: 0 auto;
}

/* Player positions */
.player {
    position: absolute;
    width: 150px;
    text-align: center;
}

.player-0 { bottom: 20px; left: 50%; transform: translateX(-50%); }
.player-1 { bottom: 100px; left: 20%; }
.player-2 { top: 30%; left: 5%; }
.player-3 { top: 10%; left: 50%; transform: translateX(-50%); }
.player-4 { top: 30%; right: 5%; }
.player-5 { bottom: 100px; right: 20%; }

/* Player info */
.player-info {
    background-color: rgba(0, 0, 0, 0.6);
    color: white;
    padding: 5px 10px;
    border-radius: 5px;
    margin-bottom: 10px;
    position: relative;
}

.player-name {
    font-weight: bold;
    margin-bottom: 5px;
}

.player-team {
    font-size: 0.9em;
    padding: 2px 5px;
    border-radius: 3px;
}

.team-kan-kei { 
    background-color: #9C89B8; 
    color: white; 
}

.team-mu-kan-kei { 
    background-color: #F0A202; 
    color: black; 
}

/* Dealer marker */
.dealer-marker {
    position: absolute;
    top: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 30px;
    background-color: #E76F51;
    border-radius: 50%;
    border: 2px solid white;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
}

.hidden { 
    display: none !important; 
}

/* Player hands */
.player-hand {
    display: flex;
    justify-content: center;
    min-height: 80px;
}

/* Current player's hand */
.current-player-hand {
    position: fixed;
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    justify-content: center;
    z-index: 100;
}

/* Cards */
.card {
    width: 60px;
    height: 90px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    margin: 0 -15px;
    transition: transform 0.2s ease;
    cursor: pointer;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
}

.card:hover {
    transform: translateY(-20px);
}

.card.playable {
    box-shadow: 0 0 10px 2px rgba(255, 255, 0, 0.7);
}

.card.selected {
    transform: translateY(-30px);
}

.card.face-down {
    background-image: url('/static/images/Cards/back01.png');
}

/* Table center for current trick */
.table-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 300px;
    height: 300px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.current-trick {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(2, 1fr);
    gap: 10px;
    width: 250px;
    height: 200px;
}

.trick-card-0 { grid-column: 2; grid-row: 2; }
.trick-card-1 { grid-column: 1; grid-row: 2; }
.trick-card-2 { grid-column: 1; grid-row: 1; }
.trick-card-3 { grid-column: 2; grid-row: 1; }
.trick-card-4 { grid-column: 3; grid-row: 1; }
.trick-card-5 { grid-column: 3; grid-row: 2; }

/* Game messages */
.game-messages {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 10px 20px;
    border-radius: 5px;
    z-index: 200;
    text-align: center;
    min-width: 300px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.game-messages.visible {
    opacity: 1;
}

/* Score display */
.score-display {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
    z-index: 300;
    width: 80%;
    max-width: 800px;
    max-height: 80vh;
    overflow-y: auto;
}

.score-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #ddd;
}

.score-table-container {
    padding: 20px;
}

.score-table {
    width: 100%;
    border-collapse: collapse;
}

.score-table th, .score-table td {
    padding: 10px;
    text-align: center;
    border: 1px solid #ddd;
}

.score-table th {
    background-color: #f5f5f5;
    font-weight: bold;
}

/* Current player indicator */
.player.current-turn .player-info {
    box-shadow: 0 0 15px 5px rgba(255, 255, 0, 0.7);
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal__content {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    width: 90%;
    max-width: 500px;
}

.modal__title {
    margin-top: 0;
    margin-bottom: 15px;
}

.modal__actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
}

.modal__actions button {
    margin-left: 10px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .player {
        width: 120px;
    }
    
    .card {
        width: 40px;
        height: 60px;
        margin: 0 -10px;
    }
    
    .player-0 { bottom: 10px; }
    .player-1 { bottom: 70px; left: 10%; }
    .player-2 { top: 30%; left: 2%; }
    .player-3 { top: 5%; }
    .player-4 { top: 30%; right: 2%; }
    .player-5 { bottom: 70px; right: 10%; }
    
    .table-center {
        width: 200px;
        height: 200px;
    }
    
    .current-trick {
        width: 180px;
        height: 150px;
    }
}
