/* Kankei Game Styles */

/* Main game container */
.kankei-game {
    width: 100%;
    height: 100vh;
    position: relative;
    overflow: hidden;
    background-color: #1C7C54; /* Green felt background */
}

/* Game information bar */
.game-info-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 20px;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
}

/* Trump indicator */
.trump-indicator {
    display: flex;
    align-items: center;
}

.trump-suit {
    width: 30px;
    height: 30px;
    margin-left: 10px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
}

.trump-suit.clubs {
    background-image: url('/static/images/Cards/clubs_01.png');
    background-size: 30px;
}

.trump-suit.diamonds {
    background-image: url('/static/images/Cards/diamonds_01.png');
    background-size: 30px;
}

.trump-suit.hearts {
    background-image: url('/static/images/Cards/hearts_01.png');
    background-size: 30px;
}

.trump-suit.spades {
    background-image: url('/static/images/Cards/spades_01.png');
    background-size: 30px;
}

/* Game table */
.game-table {
    position: relative;
    width: 100%;
    height: calc(100vh - 200px);
    margin: 0 auto;
}

/* Player positions - Perfect circle arrangement */
.player {
    position: absolute;
    width: 150px;
    text-align: center;
}

/* Circular positioning using trigonometry for perfect circle */
.player-0 { bottom: 20px; left: 50%; transform: translateX(-50%); } /* Bottom center */
.player-1 { bottom: 80px; left: 15%; transform: translateX(-50%); } /* Bottom left */
.player-2 { top: 35%; left: 8%; transform: translateY(-50%); } /* Left center */
.player-3 { top: 20px; left: 50%; transform: translateX(-50%); } /* Top center */
.player-4 { top: 35%; right: 8%; transform: translateY(-50%); } /* Right center */
.player-5 { bottom: 80px; right: 15%; transform: translateX(50%); } /* Bottom right */

/* Player info */
.player-info {
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 8px 12px;
    border-radius: 8px;
    margin-bottom: 12px;
    position: relative;
    border: 2px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.player-name {
    font-weight: bold;
    margin-bottom: 6px;
    font-size: 1.1em;
}

.player-team {
    font-size: 0.9em;
    padding: 4px 8px;
    border-radius: 5px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.team-kan-kei {
    background-color: #9C89B8;
    color: white;
    box-shadow: 0 2px 4px rgba(156, 137, 184, 0.4);
}

.team-mu-kan-kei {
    background-color: #F0A202;
    color: black;
    box-shadow: 0 2px 4px rgba(240, 162, 2, 0.4);
}

/* Dealer marker */
.dealer-marker {
    position: absolute;
    top: -18px;
    left: 50%;
    transform: translateX(-50%);
    width: 36px;
    height: 36px;
    background: linear-gradient(135deg, #E76F51, #D63031);
    border-radius: 50%;
    border: 3px solid white;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 1.1em;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.4);
    z-index: 10;
}

.hidden {
    display: none !important;
}

/* Player hands */
.player-hand {
    display: flex;
    justify-content: center;
    min-height: 80px;
}

/* Current player's hand */
.current-player-hand {
    position: fixed;
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    justify-content: center;
    z-index: 100;
}

/* Cards */
.card {
    width: 60px;
    height: 90px;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    margin: 0 -15px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    cursor: pointer;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
}

.card:hover {
    transform: translateY(-10px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
}

/* Playable cards are lifted upward instead of glowing */
.card.playable {
    transform: translateY(-15px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.6);
}

.card.playable:hover {
    transform: translateY(-20px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.7);
}

.card.selected {
    transform: translateY(-25px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.8);
}

.card.face-down {
    background-image: url('/static/images/Cards/back01.png');
}

/* Table center for current trick */
.table-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 300px;
    height: 300px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.current-trick {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(2, 1fr);
    gap: 10px;
    width: 250px;
    height: 200px;
}

.trick-card-0 { grid-column: 2; grid-row: 2; }
.trick-card-1 { grid-column: 1; grid-row: 2; }
.trick-card-2 { grid-column: 1; grid-row: 1; }
.trick-card-3 { grid-column: 2; grid-row: 1; }
.trick-card-4 { grid-column: 3; grid-row: 1; }
.trick-card-5 { grid-column: 3; grid-row: 2; }

/* Game messages */
.game-messages {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 10px 20px;
    border-radius: 5px;
    z-index: 200;
    text-align: center;
    min-width: 300px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.game-messages.visible {
    opacity: 1;
}

/* Score display */
.score-display {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: white;
    border-radius: 15px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.6);
    z-index: 300;
    width: 90%;
    max-width: 1000px;
    max-height: 85vh;
    overflow-y: auto;
}

.score-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 2px solid #e0e0e0;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 15px 15px 0 0;
}

.score-header h3 {
    margin: 0;
    color: #2c3e50;
    font-size: 1.5em;
}

.score-table-container {
    padding: 25px;
    background-color: #fafafa;
}

.score-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.score-table th {
    background: linear-gradient(135deg, #34495e, #2c3e50);
    color: white;
    padding: 15px 12px;
    text-align: center;
    font-weight: 600;
    font-size: 1.1em;
    border: none;
}

.score-table th:first-child {
    width: 80px;
    background: linear-gradient(135deg, #8e44ad, #9b59b6);
}

.score-table td {
    padding: 12px;
    text-align: center;
    border: 1px solid #e0e0e0;
    background-color: white;
    position: relative;
    vertical-align: middle;
}

.score-table tr:nth-child(even) td {
    background-color: #f8f9fa;
}

.score-table tr:hover td {
    background-color: #e3f2fd;
}

/* Trump suit column styling */
.trump-suit-cell {
    background: linear-gradient(135deg, #667eea, #764ba2) !important;
    color: white;
    font-weight: bold;
    font-size: 1.2em;
    width: 80px;
}

/* Player score cells */
.player-score-cell {
    font-size: 1.3em;
    font-weight: bold;
    color: #2c3e50;
    min-height: 50px;
}

/* Round points (small number in top-left) */
.round-points {
    position: absolute;
    top: 3px;
    left: 5px;
    font-size: 0.7em;
    color: #7f8c8d;
    background-color: rgba(52, 152, 219, 0.1);
    padding: 2px 4px;
    border-radius: 3px;
    min-width: 15px;
}

/* Cumulative points (main number) */
.cumulative-points {
    margin-top: 8px;
}

/* Trump suit icons in score table */
.trump-suit-icon {
    width: 30px;
    height: 30px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    margin: 0 auto;
    filter: brightness(0) invert(1); /* Make icons white */
}

.trump-suit-icon.clubs {
    background-image: url('/static/images/Cards/clubs_01.png');
}

.trump-suit-icon.diamonds {
    background-image: url('/static/images/Cards/diamonds_01.png');
}

.trump-suit-icon.hearts {
    background-image: url('/static/images/Cards/hearts_01.png');
}

.trump-suit-icon.spades {
    background-image: url('/static/images/Cards/spades_01.png');
}

/* Current player indicator */
.player.current-turn .player-info {
    border: 3px solid #FFD700;
    box-shadow: 0 0 20px 8px rgba(255, 215, 0, 0.6);
    background-color: rgba(255, 215, 0, 0.1);
    animation: pulse-glow 2s infinite;
}

@keyframes pulse-glow {
    0%, 100% {
        box-shadow: 0 0 20px 8px rgba(255, 215, 0, 0.6);
    }
    50% {
        box-shadow: 0 0 30px 12px rgba(255, 215, 0, 0.8);
    }
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal__content {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    width: 90%;
    max-width: 500px;
}

.modal__title {
    margin-top: 0;
    margin-bottom: 15px;
}

.modal__actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
}

.modal__actions button {
    margin-left: 10px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .player {
        width: 120px;
    }

    .card {
        width: 45px;
        height: 65px;
        margin: 0 -12px;
    }

    /* Adjust circular positioning for mobile */
    .player-0 { bottom: 15px; left: 50%; transform: translateX(-50%); }
    .player-1 { bottom: 60px; left: 12%; transform: translateX(-50%); }
    .player-2 { top: 30%; left: 5%; transform: translateY(-50%); }
    .player-3 { top: 15px; left: 50%; transform: translateX(-50%); }
    .player-4 { top: 30%; right: 5%; transform: translateY(-50%); }
    .player-5 { bottom: 60px; right: 12%; transform: translateX(50%); }

    .table-center {
        width: 200px;
        height: 200px;
    }

    .current-trick {
        width: 180px;
        height: 150px;
    }

    .game-info-bar {
        padding: 8px 15px;
        font-size: 0.9em;
    }

    .trump-suit {
        width: 25px;
        height: 25px;
    }

    .dealer-marker {
        width: 30px;
        height: 30px;
        top: -15px;
    }

    .score-display {
        width: 95%;
        max-height: 90vh;
    }

    .score-table th, .score-table td {
        padding: 8px 6px;
        font-size: 0.9em;
    }

    .trump-suit-icon {
        width: 25px;
        height: 25px;
    }
}
