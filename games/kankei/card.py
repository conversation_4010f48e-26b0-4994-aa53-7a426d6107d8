"""
Card and Deck classes for the Kankei card game.
"""
import random


class Card:
    """
    Represents a playing card in the Kankei game.
    """
    SUITS = ['clubs', 'diamonds', 'hearts', 'spades']
    RANKS = list(range(1, 14))  # 1-13 (Ace through King)
    
    def __init__(self, suit=None, rank=None, is_joker=False):
        """
        Initialize a card with a suit and rank, or as a joker.
        
        Args:
            suit (str): The card suit ('clubs', 'diamonds', 'hearts', 'spades')
            rank (int): The card rank (1-13, where 1=<PERSON>, 11=<PERSON>, 12=<PERSON>, 13=<PERSON>)
            is_joker (bool): Whether this card is a joker
        """
        self.suit = suit
        self.rank = rank
        self.is_joker = is_joker
        
    def __str__(self):
        """Return a string representation of the card."""
        if self.is_joker:
            return "Joker"
        
        rank_names = {
            1: "Ace",
            11: "Jack",
            12: "Queen",
            13: "King"
        }
        
        rank_str = rank_names.get(self.rank, str(self.rank))
        return f"{rank_str} of {self.suit.capitalize()}"
    
    def __eq__(self, other):
        """Check if two cards are equal."""
        if not isinstance(other, Card):
            return False
        
        if self.is_joker and other.is_joker:
            return True
            
        return (self.suit == other.suit and 
                self.rank == other.rank and 
                self.is_joker == other.is_joker)
    
    def get_image_path(self):
        """Get the path to the card image."""
        if self.is_joker:
            return "static/images/Cards/joker.png"
        
        # Format: clubs_01.png, hearts_13.png, etc.
        rank_str = f"{self.rank:02d}"
        return f"static/images/Cards/{self.suit}_{rank_str}.png"
    
    def is_face_card(self):
        """Check if the card is a face card (Jack, Queen, King)."""
        return self.rank in [11, 12, 13]  # Jack, Queen, King
    
    def compare_to(self, other, lead_suit, trump_suit):
        """
        Compare this card to another based on Kankei rules.
        
        Args:
            other (Card): The card to compare to
            lead_suit (str): The suit led in the current trick
            trump_suit (str): The current trump suit
            
        Returns:
            int: 1 if this card is higher, -1 if other is higher, 0 if equal
        """
        # Joker is highest
        if self.is_joker:
            return 1
        if other.is_joker:
            return -1
        
        # Trump beats non-trump
        if self.suit == trump_suit and other.suit != trump_suit:
            return 1
        if self.suit != trump_suit and other.suit == trump_suit:
            return -1
        
        # If both are trump or both are not trump
        if self.suit == other.suit:
            # Higher rank wins
            if self.rank > other.rank:
                return 1
            elif self.rank < other.rank:
                return -1
            else:
                return 0
        
        # If lead suit beats non-lead, non-trump suit
        if self.suit == lead_suit and other.suit != lead_suit and other.suit != trump_suit:
            return 1
        if self.suit != lead_suit and other.suit == lead_suit and self.suit != trump_suit:
            return -1
        
        # Different suits, neither is trump or lead
        return 0  # Effectively, the first played card wins


class Deck:
    """
    Represents a deck of cards for the Kankei game.
    """
    def __init__(self):
        """Initialize an empty deck."""
        self.cards = []
        
    def initialize(self):
        """
        Create the 50-card deck for Kankei:
        - Standard 52-card deck
        - Remove 2 of Clubs, 2 of Diamonds, and 2 of Hearts
        - Add one Joker
        """
        self.cards = []
        
        # Add all cards except the removed 2s
        for suit in Card.SUITS:
            for rank in Card.RANKS:
                # Skip 2 of Clubs, 2 of Diamonds, and 2 of Hearts
                if rank == 2 and suit in ['clubs', 'diamonds', 'hearts']:
                    continue
                self.cards.append(Card(suit, rank))
        
        # Add one Joker
        self.cards.append(Card(is_joker=True))
        
        # Verify we have exactly 50 cards
        assert len(self.cards) == 50, f"Deck has {len(self.cards)} cards instead of 50"
    
    def shuffle(self):
        """Randomize the order of cards in the deck."""
        random.shuffle(self.cards)
    
    def deal(self, num_players, cards_per_player=10):
        """
        Deal cards to players.
        
        Args:
            num_players (int): Number of players to deal to
            cards_per_player (int): Number of cards per player
            
        Returns:
            list: Lists of cards for each player
        """
        if num_players * cards_per_player > len(self.cards):
            raise ValueError(f"Not enough cards to deal {cards_per_player} cards to {num_players} players")
        
        hands = [[] for _ in range(num_players)]
        
        # Deal cards one at a time to each player
        for i in range(cards_per_player):
            for player_idx in range(num_players):
                hands[player_idx].append(self.cards.pop())
        
        return hands
