"""
Game logic for the Kankei card game.
"""
import random
from games.kankei.card import Card, Deck
from games.kankei.player import KankeiPlayer


class KankeiGame:
    """
    Main game controller for the Kankei card game.
    """
    # Game states
    STATE_WAITING = 'waiting'
    STATE_DEALING = 'dealing'
    STATE_TEAM_ASSIGNMENT = 'team_assignment'
    STATE_PLAYING = 'playing'
    STATE_TRICK_EVALUATION = 'trick_evaluation'
    STATE_ROUND_END = 'round_end'
    STATE_GAME_END = 'game_end'

    def __init__(self, game_id):
        """
        Initialize a new Kankei game.

        Args:
            game_id (str): Unique identifier for the game
        """
        self.game_id = game_id
        self.players = []  # List of KankeiPlayer objects
        self.deck = Deck()
        self.current_trick = []  # Cards played in the current trick
        self.tricks = []  # Completed tricks
        self.trump_suit = None  # Current trump suit
        self.dealer_index = None  # Index of the current dealer
        self.current_player_index = None  # Index of the player whose turn it is
        self.game_state = self.STATE_WAITING
        self.kan_kei_team = []  # Players on the Kan-kei team
        self.mu_kan_kei_team = []  # Players on the Mu-kan-kei team
        self.lead_suit = None  # Suit led in the current trick
        self.trick_number = 0  # Current trick number (1-10)
        self.trump_rotation = ['clubs', 'diamonds', 'hearts', 'spades']  # Order of trump rotation
        self.trump_index = 0  # Index in the trump rotation

    def add_player(self, player):
        """
        Add a player to the game.

        Args:
            player (KankeiPlayer): The player to add

        Returns:
            bool: True if the player was added, False otherwise
        """
        if len(self.players) >= 6:
            return False

        # Assign seat position
        player.seat_position = len(self.players)
        self.players.append(player)
        return True

    def remove_player(self, player_id):
        """
        Remove a player from the game.

        Args:
            player_id (str): ID of the player to remove

        Returns:
            KankeiPlayer: The removed player, or None if not found
        """
        for i, player in enumerate(self.players):
            if player.id == player_id:
                return self.players.pop(i)
        return None

    def start_game(self):
        """
        Start the game.

        Returns:
            bool: True if the game was started, False otherwise
        """
        if len(self.players) != 6:
            return False

        # Select initial dealer randomly
        self.dealer_index = random.randint(0, 5)
        self.players[self.dealer_index].is_dealer = True

        # Set initial trump suit
        self.trump_index = 0
        self.trump_suit = self.trump_rotation[self.trump_index]

        # Deal cards
        self.game_state = self.STATE_DEALING
        self.deal_cards()

        # Determine teams
        self.game_state = self.STATE_TEAM_ASSIGNMENT
        self.determine_teams()

        # Start first trick
        self.game_state = self.STATE_PLAYING
        self.trick_number = 1
        # Player to the right of dealer starts (skip dealer if needed)
        self.current_player_index = (self.dealer_index + 1) % 6
        # Make sure we don't start with the dealer
        if self.current_player_index == self.dealer_index:
            self.current_player_index = (self.current_player_index + 1) % 6
        self.lead_suit = None

        return True

    def deal_cards(self):
        """
        Deal cards to all players except the dealer.
        In Kankei, the dealer doesn't receive cards.
        The 50 cards are dealt to the 5 non-dealer players (10 cards each).
        """
        self.deck.initialize()
        self.deck.shuffle()

        # Get non-dealer players (5 players)
        non_dealer_players = [p for i, p in enumerate(self.players) if i != self.dealer_index]

        # Deal 10 cards to each non-dealer player (5 players × 10 cards = 50 cards)
        hands = self.deck.deal(len(non_dealer_players), 10)
        for i, player in enumerate(non_dealer_players):
            player.hand = hands[i]

        # Dealer gets no cards
        self.players[self.dealer_index].hand = []

    def determine_teams(self):
        """
        Assign players to teams based on who has the trump Ace and Joker.
        """
        # Reset teams
        self.kan_kei_team = []
        self.mu_kan_kei_team = []

        # Find players with trump Ace and Joker (only among non-dealer players)
        trump_ace_holder = None
        joker_holder = None

        # Only check non-dealer players since dealer has no cards
        non_dealer_players = [p for i, p in enumerate(self.players) if i != self.dealer_index]

        for player in non_dealer_players:
            # Check for trump Ace
            if player.has_card(self.trump_suit, 1):
                trump_ace_holder = player

            # Check for Joker
            if player.has_card(None, None, True):
                joker_holder = player

        # Dealer is always on the Kan-kei team
        dealer = self.players[self.dealer_index]

        # Handle case where one player has both trump Ace and Joker
        if trump_ace_holder == joker_holder:
            # Find player two seats to the right (among non-dealer players)
            trade_index = (trump_ace_holder.seat_position + 2) % 6
            trade_player = next((p for p in non_dealer_players if p.seat_position == trade_index), None)

            # If no player found at the calculated position, find any other non-dealer player
            if trade_player is None:
                for player in non_dealer_players:
                    if player != trump_ace_holder:
                        trade_player = player
                        break

            # Only proceed with trade if we found a valid trade partner
            if trade_player is not None:
                # Randomly choose which card to trade
                if random.choice([True, False]):
                    # Trade trump Ace
                    ace = trump_ace_holder.get_card(self.trump_suit, 1)
                    trump_ace_holder.remove_card(ace)
                    trade_player.add_card(ace)
                    trump_ace_holder = trade_player
                else:
                    # Trade Joker
                    joker = trump_ace_holder.get_card(None, None, True)
                    trump_ace_holder.remove_card(joker)
                    trade_player.add_card(joker)
                    joker_holder = trade_player

        # Assign teams
        self.kan_kei_team = [dealer, trump_ace_holder, joker_holder]

        # All other players are on the Mu-kan-kei team
        self.mu_kan_kei_team = [p for p in self.players if p not in self.kan_kei_team]

        # Update team assignments in player objects
        for player in self.kan_kei_team:
            player.team = 'kan-kei'

        for player in self.mu_kan_kei_team:
            player.team = 'mu-kan-kei'

        # Check team seating and adjust if needed
        self.check_team_seating()

    def check_team_seating(self):
        """
        Check if Kan-kei players are seated adjacently and adjust if needed.
        Only consider non-dealer players for seating adjustments.
        """
        # Get seat positions of non-dealer Kan-kei team members
        non_dealer_kan_kei = [p for p in self.kan_kei_team if p != self.players[self.dealer_index]]
        kan_kei_positions = sorted([p.seat_position for p in non_dealer_kan_kei])

        # Check if any two non-dealer Kan-kei players are adjacent
        for i in range(len(kan_kei_positions)):
            pos1 = kan_kei_positions[i]
            pos2 = kan_kei_positions[(i + 1) % len(kan_kei_positions)]

            # Check if positions are adjacent (considering wrap-around)
            if (pos2 - pos1) % 6 == 1 or (pos1 - pos2) % 6 == 1:
                # Find the right player to swap
                right_player = next((p for p in non_dealer_kan_kei if p.seat_position == pos2), None)

                if right_player is None:
                    # If no player found, skip this adjustment
                    continue

                # Find player two seats to the right (among non-dealer players)
                swap_position = (pos2 + 2) % 6
                non_dealer_players = [p for i, p in enumerate(self.players) if i != self.dealer_index]
                swap_player = next((p for p in non_dealer_players if p.seat_position == swap_position), None)

                # If no player found at the calculated position, find any non-adjacent player
                if swap_player is None:
                    for player in non_dealer_players:
                        if player not in non_dealer_kan_kei:
                            swap_player = player
                            break

                if swap_player is None:
                    # If still no player found, skip the seating adjustment
                    continue

                # Swap positions
                right_player.seat_position, swap_player.seat_position = swap_player.seat_position, right_player.seat_position

                # No need to continue checking after a swap
                break

    def play_card(self, player_id, card_index):
        """
        Process a player playing a card.

        Args:
            player_id (str): ID of the player playing the card
            card_index (int): Index of the card in the player's hand

        Returns:
            dict: Result of the play, including success status and game state updates
        """
        if self.game_state != self.STATE_PLAYING:
            return {'success': False, 'error': 'Not in playing state'}

        # Find the player
        player = next((p for p in self.players if p.id == player_id), None)
        if not player:
            return {'success': False, 'error': 'Player not found'}

        # Check if it's the player's turn
        if self.players[self.current_player_index].id != player_id:
            return {'success': False, 'error': 'Not your turn'}

        # Check if the card index is valid
        if card_index < 0 or card_index >= len(player.hand):
            return {'success': False, 'error': 'Invalid card index'}

        # Get the card
        card = player.hand[card_index]

        # Check if the card is playable
        playable_cards = player.get_playable_cards(self.lead_suit, self.trump_suit)
        if card not in playable_cards:
            return {'success': False, 'error': 'Card not playable'}

        # Remove the card from the player's hand
        played_card = player.play_card(card)

        # Add the card to the current trick
        self.current_trick.append({
            'card': played_card,
            'player_id': player_id
        })

        # If this is the first card in the trick, set the lead suit
        if len(self.current_trick) == 1:
            self.lead_suit = played_card.suit

        # Check if the trick is complete (5 cards since dealer doesn't play)
        if len(self.current_trick) == 5:
            self.game_state = self.STATE_TRICK_EVALUATION
            return self.evaluate_trick()

        # Move to the next player (skip dealer)
        self.current_player_index = (self.current_player_index + 1) % 6
        # Skip dealer if we land on them
        if self.current_player_index == self.dealer_index:
            self.current_player_index = (self.current_player_index + 1) % 6

        return {
            'success': True,
            'card_played': {
                'suit': played_card.suit,
                'rank': played_card.rank,
                'is_joker': played_card.is_joker,
                'image_path': played_card.get_image_path()
            },
            'next_player_id': self.players[self.current_player_index].id,
            'trick_size': len(self.current_trick)
        }

    def evaluate_trick(self):
        """
        Determine the winner of the current trick.

        Returns:
            dict: Result of the evaluation, including the winner and updated game state
        """
        # Find the winning card
        winning_card_index = 0
        lead_suit = self.current_trick[0]['card'].suit

        for i in range(1, len(self.current_trick)):
            current_card = self.current_trick[i]['card']
            winning_card = self.current_trick[winning_card_index]['card']

            comparison = current_card.compare_to(winning_card, lead_suit, self.trump_suit)
            if comparison > 0:
                winning_card_index = i

        # Get the winner
        winner_id = self.current_trick[winning_card_index]['player_id']
        winner = next(p for p in self.players if p.id == winner_id)

        # Add the trick to the winner's collection
        winner.add_trick([entry['card'] for entry in self.current_trick])

        # Store the completed trick
        self.tricks.append({
            'cards': self.current_trick,
            'winner_id': winner_id
        })

        # Prepare for the next trick
        self.current_trick = []
        self.lead_suit = None
        self.trick_number += 1

        # Set the winner as the next player to lead
        self.current_player_index = next(i for i, p in enumerate(self.players) if p.id == winner_id)

        # Check if the round is over (10 tricks)
        if self.trick_number > 10:
            self.game_state = self.STATE_ROUND_END
            return self.end_round()

        # Continue playing
        self.game_state = self.STATE_PLAYING

        return {
            'success': True,
            'trick_winner': {
                'id': winner_id,
                'name': winner.name
            },
            'next_player_id': winner_id,
            'trick_number': self.trick_number,
            'game_state': self.game_state
        }

    def end_round(self):
        """
        End the current round and calculate scores.

        Returns:
            dict: Result of the round, including scores and updated game state
        """
        # Calculate scores for each player
        for player in self.players:
            player.score = player.calculate_score()

        # Rotate dealer
        self.players[self.dealer_index].is_dealer = False
        self.dealer_index = (self.dealer_index + 1) % 6
        self.players[self.dealer_index].is_dealer = True

        # Rotate trump suit
        self.trump_index = (self.trump_index + 1) % len(self.trump_rotation)
        self.trump_suit = self.trump_rotation[self.trump_index]

        # Reset for next round
        self.trick_number = 0
        self.tricks = []

        # Return round results
        return {
            'success': True,
            'scores': {player.id: player.score for player in self.players},
            'new_dealer': {
                'id': self.players[self.dealer_index].id,
                'name': self.players[self.dealer_index].name
            },
            'new_trump_suit': self.trump_suit,
            'game_state': self.game_state
        }

    def get_game_state(self, player_id=None):
        """
        Get the current game state for client updates.

        Args:
            player_id (str): ID of the player requesting the state (to include their hand)

        Returns:
            dict: Current game state
        """
        state = {
            'game_id': self.game_id,
            'game_state': self.game_state,
            'trump_suit': self.trump_suit,
            'trick_number': self.trick_number,
            'current_trick': [
                {
                    'card': {
                        'suit': entry['card'].suit,
                        'rank': entry['card'].rank,
                        'is_joker': entry['card'].is_joker,
                        'image_path': entry['card'].get_image_path()
                    },
                    'player_id': entry['player_id']
                }
                for entry in self.current_trick
            ],
            'lead_suit': self.lead_suit,
            'players': [
                player.to_dict(include_hand=(player.id == player_id))
                for player in sorted(self.players, key=lambda p: p.seat_position)
            ],
            'current_player_id': self.players[self.current_player_index].id if self.current_player_index is not None else None,
            'dealer_id': self.players[self.dealer_index].id if self.dealer_index is not None else None,
            'kan_kei_team': [p.id for p in self.kan_kei_team],
            'mu_kan_kei_team': [p.id for p in self.mu_kan_kei_team]
        }

        # If a specific player is requesting, include their playable cards
        if player_id and self.game_state == self.STATE_PLAYING:
            player = next((p for p in self.players if p.id == player_id), None)
            if player:
                playable_cards = player.get_playable_cards(self.lead_suit, self.trump_suit)
                state['playable_cards'] = [
                    player.hand.index(card) for card in playable_cards
                ]

        return state
