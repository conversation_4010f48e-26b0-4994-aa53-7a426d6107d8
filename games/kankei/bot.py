"""
Bot player implementation for the Kankei card game.
"""
import random
from games.kankei.player import KankeiPlayer


class KankeiBot(KankeiPlayer):
    """
    AI player implementation for the Kankei game.
    """
    def __init__(self, id, name=None):
        """
        Initialize a bot player.
        
        Args:
            id (str): Bot identifier
            name (str): Bot name (generated if None)
        """
        if name is None:
            name = f"Bot-{id[-4:]}"
        
        super().__init__(id, name)
        self.is_bot = True
    
    def choose_card(self, game_state):
        """
        Choose a card to play based on the current game state.
        
        Args:
            game_state (dict): Current game state
            
        Returns:
            int: Index of the card to play
        """
        # Get playable cards
        playable_indices = game_state.get('playable_cards', [])
        
        if not playable_indices:
            return None
        
        # For now, just choose a random card from the playable ones
        # This is a placeholder for more sophisticated AI logic
        return random.choice(playable_indices)
    
    def to_dict(self, include_hand=False):
        """
        Convert bot to a dictionary for JSON serialization.
        
        Args:
            include_hand (bool): Whether to include the bot's hand
            
        Returns:
            dict: Dictionary representation of the bot
        """
        bot_dict = super().to_dict(include_hand)
        bot_dict['is_bot'] = True
        return bot_dict
