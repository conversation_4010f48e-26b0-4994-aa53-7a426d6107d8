"""
Player class for the Kankei card game.
"""
from games.kankei.card import Card


class KankeiPlayer:
    """
    Represents a player in the Kankei game.
    """
    def __init__(self, id, name):
        """
        Initialize a player.
        
        Args:
            id (str): Player identifier
            name (str): Player name
        """
        self.id = id
        self.name = name
        self.hand = []
        self.team = None  # 'kan-kei' or 'mu-kan-kei'
        self.is_dealer = False
        self.tricks_won = []
        self.score = 0
        self.seat_position = None  # 0-5 for the 6 players
    
    def add_card(self, card):
        """
        Add a card to the player's hand.
        
        Args:
            card (Card): The card to add
        """
        self.hand.append(card)
    
    def remove_card(self, card):
        """
        Remove a card from the player's hand.
        
        Args:
            card (Card): The card to remove
            
        Returns:
            Card: The removed card, or None if not found
        """
        for i, c in enumerate(self.hand):
            if c == card:
                return self.hand.pop(i)
        return None
    
    def get_playable_cards(self, lead_suit=None, trump_suit=None):
        """
        Get cards that can be played based on the current trick.
        
        Args:
            lead_suit (str): The suit led in the current trick
            trump_suit (str): The current trump suit
            
        Returns:
            list: List of playable cards
        """
        # If this is the first card in the trick, any card can be played
        if lead_suit is None:
            return self.hand.copy()
        
        # Check if player has any cards of the lead suit
        has_lead_suit = any(card.suit == lead_suit for card in self.hand)
        
        # If player has cards of the lead suit, they must play one
        if has_lead_suit:
            return [card for card in self.hand if card.suit == lead_suit]
        
        # If player doesn't have the lead suit, they can play any card
        return self.hand.copy()
    
    def play_card(self, card):
        """
        Play a card from the hand.
        
        Args:
            card (Card): The card to play
            
        Returns:
            Card: The played card, or None if not found or not playable
        """
        return self.remove_card(card)
    
    def add_trick(self, trick):
        """
        Add a won trick to the player's collection.
        
        Args:
            trick (list): List of cards in the trick
        """
        self.tricks_won.append(trick)
    
    def calculate_score(self):
        """
        Calculate the player's score based on tricks won.
        
        Returns:
            int: The player's score
        """
        score = 0
        
        # Count face cards in tricks won
        for trick in self.tricks_won:
            for card in trick:
                if card.is_face_card():
                    score += 1
        
        return score
    
    def has_card(self, suit, rank=None, is_joker=False):
        """
        Check if the player has a specific card.
        
        Args:
            suit (str): The card suit
            rank (int): The card rank
            is_joker (bool): Whether to check for a joker
            
        Returns:
            bool: True if the player has the card, False otherwise
        """
        if is_joker:
            return any(card.is_joker for card in self.hand)
        
        return any(card.suit == suit and card.rank == rank for card in self.hand)
    
    def get_card(self, suit, rank=None, is_joker=False):
        """
        Get a specific card from the player's hand.
        
        Args:
            suit (str): The card suit
            rank (int): The card rank
            is_joker (bool): Whether to get a joker
            
        Returns:
            Card: The card, or None if not found
        """
        for card in self.hand:
            if is_joker and card.is_joker:
                return card
            
            if card.suit == suit and card.rank == rank:
                return card
        
        return None
    
    def to_dict(self, include_hand=False):
        """
        Convert player to a dictionary for JSON serialization.
        
        Args:
            include_hand (bool): Whether to include the player's hand
            
        Returns:
            dict: Dictionary representation of the player
        """
        player_dict = {
            'id': self.id,
            'name': self.name,
            'team': self.team,
            'is_dealer': self.is_dealer,
            'score': self.score,
            'seat_position': self.seat_position,
            'tricks_won_count': len(self.tricks_won)
        }
        
        if include_hand:
            player_dict['hand'] = [
                {
                    'suit': card.suit,
                    'rank': card.rank,
                    'is_joker': card.is_joker,
                    'image_path': card.get_image_path()
                }
                for card in self.hand
            ]
        
        return player_dict
